import numpy as np
import pandas as pd

print("=== PS面 Utau 计算程序 ===")

# 基本参数设置
Re = 1.645e5  # 雷诺数
miu = 1       # 动力粘度
rows, cols = 578, 171

# 读取PS面数据文件
data_file = r'../../Alltont_PS/Allnt_avg.dat'
print(f"正在读取数据文件：{data_file}")

try:
    data = np.loadtxt(data_file, skiprows=2)
    print("数据文件读取成功！")
except Exception as e:
    print(f"读取数据文件失败：{e}")
    exit()

# 从数据中提取y坐标和u速度分量
y00_arr = data[:, 1].reshape(cols, rows).T  # y坐标
u00_arr = data[:, 2].reshape(cols, rows).T  # u速度分量

print(f"数据矩阵大小：{y00_arr.shape}")

# 添加边界层附近的点（增加1列用于壁面附近插值）
def add_columns(arr, num_cols):
    return np.pad(arr, ((0, 0), (num_cols, 0)), mode='constant', constant_values=0)

y0_arr = add_columns(y00_arr, 1)
u0_arr = add_columns(u00_arr, 1)

# 壁面附近点的插值处理
factor = 0.6
y0_arr[:, 1] = y0_arr[:, 2] * factor
u0_arr[:, 1] = u0_arr[:, 2] * factor

factor2 = 0.55
y0_arr[:, 2] = y0_arr[:, 1] * factor2 + y0_arr[:, 3] * (1 - factor2)
u0_arr[:, 2] = u0_arr[:, 1] * factor2 + u0_arr[:, 3] * (1 - factor2)

print("边界层附近点插值处理完成")

# 计算速度梯度du/dy（使用三点差分）
k1_arr = (u0_arr[:, 3] - u0_arr[:, 2]) / (y0_arr[:, 3] - y0_arr[:, 2])
k2_arr = (u0_arr[:, 4] - u0_arr[:, 3]) / (y0_arr[:, 4] - y0_arr[:, 3])
k3_arr = (u0_arr[:, 5] - u0_arr[:, 4]) / (y0_arr[:, 5] - y0_arr[:, 4])

# 平均速度梯度
kavg_arr = (k1_arr + k2_arr + k3_arr) / 3

# 计算壁面剪切应力
tauw_arr = miu * kavg_arr
tauw_arr = np.abs(tauw_arr)  # 取绝对值
tauw_arr = np.maximum(tauw_arr, 1e-6)  # 防止除零错误

# 计算摩擦速度Utau
utau_arr = np.sqrt(tauw_arr / Re)

print("Utau计算完成！")
print(f"Utau范围：{np.min(utau_arr):.6f} ~ {np.max(utau_arr):.6f}")
print(f"Utau均值：{np.mean(utau_arr):.6f}")

# 创建流向位置索引
x_index = np.arange(1, len(utau_arr) + 1)

# 输出结果到Excel文件
print("正在保存结果到Excel文件...")

output_data = {
    'x_index': x_index,
    'tauw_PS': tauw_arr,
    'utau_PS': utau_arr,
    'du_dy_PS': kavg_arr
}

df = pd.DataFrame(output_data)
output_filename = 'Utau_PS_Results.xlsx'
df.to_excel(output_filename, index=False)

print(f"PS面Utau计算结果已保存到：{output_filename}")
print("包含以下列：")
print("  - x_index: 流向位置索引")
print("  - tauw_PS: PS面壁面剪切应力")
print("  - utau_PS: PS面摩擦速度")
print("  - du_dy_PS: PS面速度梯度")

print("\n=== 计算完成 ===")
print(f"总数据点数：{len(utau_arr)}")
print(f"壁面剪切应力范围：{np.min(tauw_arr):.6f} ~ {np.max(tauw_arr):.6f}")
print(f"速度梯度范围：{np.min(kavg_arr):.6f} ~ {np.max(kavg_arr):.6f}") 