import numpy as np
import pandas as pd

# 跳过前两行读取数据文件
data = np.loadtxt(r'../../Alltont_PS/Allnt_avg.dat', skiprows=2)
data2 = np.loadtxt(r'../FortranExtr_PS/xs_PS.dat', skiprows=2)
df = pd.read_excel(r'../../Htheta/Htheta_PS/Htheta_PS.xlsx')  # 读取速度，温度边界层积分量
xl_arr = df['x/L'].values
BLdelta99_arr = df['delta99'].values # 读取边界层名义厚度
BLtheta_arr = df['theta'].values # 读取边界层动量厚度
BLh = df['H'].values # 读取边界层形状因子

#todo 不同雷诺数要修改
Re = 1.645 * 10**5
miu = 1
niu = 1 / Re
Pr = 0.71
rho = 1000.0  # 添加密度参数
gamma = 100000  # 添加gamma参数
# gamma = 1  # 添加gamma参数
# 提取变量
rows, cols = 578, 171
# 按列读取数据
x00_arr = data2[:, 0].reshape(cols, rows).T # 此为曲面s的长度
y00_arr = data[:, 1].reshape(cols, rows).T
u00_arr = data[:, 2].reshape(cols, rows).T
v00_arr = data[:, 3].reshape(cols, rows).T
p00_arr = data[:, 4].reshape(cols, rows).T
uu00_arr = data[:, 5].reshape(cols, rows).T
uv00_arr = data[:, 6].reshape(cols, rows).T
vv00_arr = data[:, 7].reshape(cols, rows).T
ww00_arr = data[:, 8].reshape(cols, rows).T
theta00_arr = data[:, 9].reshape(cols, rows).T
theta200_arr = data[:, 10].reshape(cols, rows).T
thetav00_arr = data[:, 12].reshape(cols, rows).T
# 增加1列
def add_columns(arr, num_cols):
    return np.pad(arr, ((0, 0), (num_cols, 0)), mode='constant', constant_values=0)
new_cols = 1
x0_arr = add_columns(x00_arr, new_cols)
y0_arr = add_columns(y00_arr, new_cols)
u0_arr = add_columns(u00_arr, new_cols)
v0_arr = add_columns(v00_arr, new_cols)
p0_arr = add_columns(p00_arr, new_cols)
uu0_arr = add_columns(uu00_arr, new_cols)
uv0_arr = add_columns(uv00_arr, new_cols)
vv0_arr = add_columns(vv00_arr, new_cols)
ww0_arr = add_columns(ww00_arr, new_cols)
theta0_arr = add_columns(theta00_arr, new_cols)
theta20_arr = add_columns(theta200_arr, new_cols)
thetav0_arr = add_columns(thetav00_arr, new_cols)
# 将前四列赋值为第五列的值，多列赋值需要先转换为二维数组才行
factor = 0.6
x0_arr[:, 1] = x0_arr[:, 2]
p0_arr[:, 1] = p0_arr[:, 2]
y0_arr[:, 1] = y0_arr[:, 2] * factor
u0_arr[:, 1] = u0_arr[:, 2] * factor
v0_arr[:, 1] = v0_arr[:, 2] * factor
uu0_arr[:, 1] = uu0_arr[:, 2] * factor
uv0_arr[:, 1] = uv0_arr[:, 2] * factor
vv0_arr[:, 1] = vv0_arr[:, 2] * factor
ww0_arr[:, 1] = ww0_arr[:, 2] * factor
theta0_arr[:, 1] = theta0_arr[:, 2] * factor
theta20_arr[:, 1] = theta20_arr[:, 2] * factor
thetav0_arr[:, 1] = thetav0_arr[:, 2] * factor
factor2 = 0.55 # 控制第二个点位置
y0_arr[:, 2] = y0_arr[:, 1] * factor2 + y0_arr[:, 3] * (1 - factor2)
u0_arr[:, 2] = u0_arr[:, 1] * factor2 + u0_arr[:, 3] * (1 - factor2)
v0_arr[:, 2] = v0_arr[:, 1] * factor2 + v0_arr[:, 3] * (1 - factor2)
uu0_arr[:, 2] = uu0_arr[:, 1] * factor2 + uu0_arr[:, 3] * (1 - factor2)
uv0_arr[:, 2] = uv0_arr[:, 1] * factor2 + uv0_arr[:, 3] * (1 - factor2)
vv0_arr[:, 2] = vv0_arr[:, 1] * factor2 + vv0_arr[:, 3] * (1 - factor2)
ww0_arr[:, 2] = ww0_arr[:, 1] * factor2 + ww0_arr[:, 3] * (1 - factor2)
theta0_arr[:, 2] = theta0_arr[:, 1] * factor2 + theta0_arr[:, 3] * (1 - factor2)
theta20_arr[:, 2] = theta20_arr[:, 1] * factor2 + theta20_arr[:, 3] * (1 - factor2)
thetav0_arr[:, 2] = thetav0_arr[:, 1] * factor2 + thetav0_arr[:, 3] * (1 - factor2)
# 求utau
k1_arr = (u0_arr[:, 3] - u0_arr[:, 2]) / (y0_arr[:, 3] - y0_arr[:, 2])
k2_arr = (u0_arr[:, 4] - u0_arr[:, 3]) / (y0_arr[:, 4] - y0_arr[:, 3])
k3_arr = (u0_arr[:, 5] - u0_arr[:, 4]) / (y0_arr[:, 5] - y0_arr[:, 4])
kavg_arr = (k1_arr + k2_arr + k3_arr) / 3
tauw_arr = miu * kavg_arr
tauw_arr = np.abs(tauw_arr)  # 取绝对值
tauw_arr = np.maximum(tauw_arr, 0.000001)  # 将0值置为极小值
utau0_arr = np.sqrt(tauw_arr / Re)

# 定义安全计算梯度的函数，以避免除零错误
def safe_grad(f_arr, d_arr, axis):
    df_arr = np.zeros_like(f_arr)
    safe_d_arr = np.where(d_arr == 0, np.nan, d_arr) # 满足为0，用nan替换；不满足则用原来数组
    if axis == 0: #求x方向导
        df_arr[1:-1, :] = (f_arr[2:, :] - f_arr[0:-2, :]) / safe_d_arr[1:-1, :]
        df_arr[0, :] = (f_arr[1, :] - f_arr[0, :]) / safe_d_arr[0, :]
        df_arr[-1, :] = (f_arr[-1, :] - f_arr[-2, :]) / safe_d_arr[-1, :]
    elif axis == 1: #求y方向导
        df_arr[:, 1:-1] = (f_arr[:, 2:] - f_arr[:, 0:-2]) / safe_d_arr[:, 1:-1]
        df_arr[:, 0] = (f_arr[:, 1] - f_arr[:, 0]) / safe_d_arr[:, 0]
        df_arr[:, -1] = (f_arr[:, -1] - f_arr[:, -2]) / safe_d_arr[:, -1]
    return np.nan_to_num(df_arr, nan=0.0, posinf=0.0, neginf=0.0) # 将偏导df_arr中的nan，正无穷，负无穷替换成0输出

# 定义距离, 外尺度求偏导，x0,y0
dx0_arr = np.zeros_like(x0_arr)
dy0_arr = np.zeros_like(y0_arr)
# 边界距离
dx0_arr[0, :] = x0_arr[1, :] - x0_arr[0, :]
dx0_arr[-1, :] = x0_arr[-1, :] - x0_arr[-2, :]
dy0_arr[:, 0] = y0_arr[:, 1] - y0_arr[:, 0]
dy0_arr[:, -1] = y0_arr[:, -1] - y0_arr[:, -2]
# 中心插值距离
dy0_arr[:, 1:-1] = y0_arr[:, 2:] - y0_arr[:, 0:-2]
dx0_arr[1:-1, :] = x0_arr[2:, :] - x0_arr[0:-2, :]

# 计算压力梯度dp/dxs并验证
dpdxs_arr = safe_grad(p0_arr, dx0_arr, axis=0)

# 计算新的特征速度尺度uc
theta_term = (np.abs(BLtheta_arr) / rho * gamma ) * np.abs(dpdxs_arr[:, 1]) #todo 增加gamma系数
uc0_arr = np.sqrt(tauw_arr/rho + theta_term)
uc_arr = uc0_arr[:, np.newaxis]

# thetatau
k4_arr = (theta0_arr[:, 3] - theta0_arr[:, 2]) / (y0_arr[:, 3] - y0_arr[:, 2])
k5_arr = (theta0_arr[:, 4] - theta0_arr[:, 3]) / (y0_arr[:, 4] - y0_arr[:, 3])
k6_arr = (theta0_arr[:, 5] - theta0_arr[:, 4]) / (y0_arr[:, 5] - y0_arr[:, 4])
dtdy_arr = (k4_arr + k5_arr + k6_arr) / 3
thetatau0_arr = dtdy_arr / ( utau0_arr * Re * Pr ) #todo 用的原始的utau_arr，不是uc_arr

utau_arr = utau0_arr[:, np.newaxis]
thetatau_arr = thetatau0_arr[:, np.newaxis]

# 计算Retau和Retheta
Retau_arr = utau0_arr * BLdelta99_arr / niu
Retheta_arr = BLtheta_arr / niu

# 边界层当地尺度无量纲化
x_arr = x0_arr * uc_arr / niu
y_arr = y0_arr * uc_arr / niu
u_arr = u0_arr / uc_arr
v_arr = v0_arr / uc_arr
p_arr = 0.5 * p0_arr / (uc_arr**2)
uu_arr = uu0_arr / (uc_arr**2)
uv_arr = uv0_arr / (uc_arr**2)
vv_arr = vv0_arr / (uc_arr**2)

ythetaplus_arr = y0_arr * uc_arr * Pr / niu
thetaplus_arr = theta0_arr / thetatau_arr
thetavplus_arr = thetav0_arr / ( thetatau_arr * uc_arr )
theta2plus_arr = theta20_arr / ( thetatau_arr * thetatau_arr )

# 定义距离，内尺度x+,y+求偏导
dx_arr = np.zeros_like(x_arr)
dy_arr = np.zeros_like(y_arr)
# 边界距离
dx_arr[0, :] = x_arr[1, :] - x_arr[0, :]
dx_arr[-1, :] = x_arr[-1, :] - x_arr[-2, :]
dy_arr[:, 0] = y_arr[:, 1] - y_arr[:, 0]
dy_arr[:, -1] = y_arr[:, -1] - y_arr[:, -2]
# 中心插值距离
dy_arr[:, 1:-1] = y_arr[:, 2:] - y_arr[:, 0:-2]
dx_arr[1:-1, :] = x_arr[2:, :] - x_arr[0:-2, :]

du_dy = safe_grad(u_arr, dy_arr, axis=1)
IDF_arr = y_arr * du_dy
dIDF_arr = y_arr * safe_grad(IDF_arr, dy_arr, axis=1)

suffix = '_178k'
combined_df = pd.DataFrame()
# 指定i_values列表
i_values = [360, 400, 427]
# 第一个Excel文件 - 包含与y相关的参数
profile_df = pd.DataFrame()
for i_fixed in i_values:
    i_index = i_fixed - 161
    df = pd.DataFrame({
        f'yn+(i{i_fixed}){suffix}': y_arr[i_index, :],
        f'ut+(i{i_fixed}){suffix}': u_arr[i_index, :],         
        f'ythe+(i{i_fixed}){suffix}': ythetaplus_arr[i_index, :],
        f'theta+(i{i_fixed}){suffix}': thetaplus_arr[i_index, :]
    })
    profile_df = pd.concat([profile_df, df], axis=1)
# 保存包含剖面数据的Excel文件
profile_df.to_excel('MTPucplus_PS_1.xlsx', index=False)

# 第二个Excel文件 - 包含流向参数
streamwise_df = pd.DataFrame({
    'x/L': xl_arr,
    'xs': x0_arr[:, 1],
    f'tauw': tauw_arr,
    f'Retau': Retau_arr,
    f'Retheta': Retheta_arr,
    f'dpdxs': dpdxs_arr[:, 1],
    f'uc': uc0_arr
})
streamwise_df.to_excel('xsVar_PS.xlsx', index=False)