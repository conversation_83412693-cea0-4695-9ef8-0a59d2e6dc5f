import numpy as np
import pandas as pd

print("=== SS面 摩擦速度与摩擦温度计算程序 ===")

# 基本参数设置
Re = 1.645e5  # 雷诺数
miu = 1       # 动力粘度
Pr = 0.71     # 普朗特数
rows, cols = 578, 230

# 读取SS面数据文件
data_file = r'../../Alltont_SS/Allnt_avg.dat'  # 假设SS面有对应的数据文件
print(f"正在读取数据文件：{data_file}")

try:
    data = np.loadtxt(data_file, skiprows=2)
    print("数据文件读取成功！")
except Exception as e:
    print(f"读取数据文件失败：{e}")
    print("如果SS面数据文件路径不同，请修改data_file变量")
    exit()

# 从数据中提取坐标、速度和温度
y00_arr = data[:, 1].reshape(cols, rows).T  # y坐标
u00_arr = data[:, 2].reshape(cols, rows).T  # u速度分量
theta00_arr = data[:, 9].reshape(cols, rows).T  # 温度场

print(f"数据矩阵大小：{y00_arr.shape}")

# 添加边界层附近的点（增加1列用于壁面附近插值）
def add_columns(arr, num_cols):
    return np.pad(arr, ((0, 0), (num_cols, 0)), mode='constant', constant_values=0)

y0_arr = add_columns(y00_arr, 1)
u0_arr = add_columns(u00_arr, 1)
theta0_arr = add_columns(theta00_arr, 1)

# 壁面附近点的插值处理
factor = 0.6
y0_arr[:, 1] = y0_arr[:, 2] * factor
u0_arr[:, 1] = u0_arr[:, 2] * factor
theta0_arr[:, 1] = theta0_arr[:, 2] * factor

factor2 = 0.55
y0_arr[:, 2] = y0_arr[:, 1] * factor2 + y0_arr[:, 3] * (1 - factor2)
u0_arr[:, 2] = u0_arr[:, 1] * factor2 + u0_arr[:, 3] * (1 - factor2)
theta0_arr[:, 2] = theta0_arr[:, 1] * factor2 + theta0_arr[:, 3] * (1 - factor2)

print("边界层附近点插值处理完成")

# === 计算摩擦速度 Utau ===
print("正在计算摩擦速度...")

# 计算速度梯度du/dy（使用三点差分）
k1_arr = (u0_arr[:, 3] - u0_arr[:, 2]) / (y0_arr[:, 3] - y0_arr[:, 2])
k2_arr = (u0_arr[:, 4] - u0_arr[:, 3]) / (y0_arr[:, 4] - y0_arr[:, 3])
k3_arr = (u0_arr[:, 5] - u0_arr[:, 4]) / (y0_arr[:, 5] - y0_arr[:, 4])

# 平均速度梯度
kavg_arr = (k1_arr + k2_arr + k3_arr) / 3

# 计算壁面剪切应力
tauw_arr = miu * kavg_arr
tauw_arr = np.abs(tauw_arr)  # 取绝对值
tauw_arr = np.maximum(tauw_arr, 1e-6)  # 防止除零错误

# 计算摩擦速度Utau
utau_arr = np.sqrt(tauw_arr / Re)

print("摩擦速度计算完成！")

# === 计算摩擦温度 θτ ===
print("正在计算摩擦温度...")

# 计算温度梯度dθ/dy（使用三点差分）
k4_arr = (theta0_arr[:, 3] - theta0_arr[:, 2]) / (y0_arr[:, 3] - y0_arr[:, 2])
k5_arr = (theta0_arr[:, 4] - theta0_arr[:, 3]) / (y0_arr[:, 4] - y0_arr[:, 3])
k6_arr = (theta0_arr[:, 5] - theta0_arr[:, 4]) / (y0_arr[:, 5] - y0_arr[:, 4])

# 平均温度梯度
dtdy_arr = (k4_arr + k5_arr + k6_arr) / 3

# 计算摩擦温度 θτ = (dθ/dy)_wall / (utau * Re * Pr)
thetatau_arr = dtdy_arr / (utau_arr * Re * Pr)
thetatau_arr = np.abs(thetatau_arr)  # 取绝对值

print("摩擦温度计算完成！")

# 输出计算结果统计
print(f"\n=== 计算结果统计 ===")
print(f"摩擦速度 Utau:")
print(f"  范围: {np.min(utau_arr):.6f} ~ {np.max(utau_arr):.6f}")
print(f"  均值: {np.mean(utau_arr):.6f}")
print(f"  标准差: {np.std(utau_arr):.6f}")

print(f"\n摩擦温度 θτ:")
print(f"  范围: {np.min(thetatau_arr):.6f} ~ {np.max(thetatau_arr):.6f}")
print(f"  均值: {np.mean(thetatau_arr):.6f}")
print(f"  标准差: {np.std(thetatau_arr):.6f}")

# 创建流向位置索引
x_index = np.arange(1, len(utau_arr) + 1)

# 输出结果到Excel文件
print("\n正在保存结果到Excel文件...")

output_data = {
    'x_index': x_index,
    'tauw_SS': tauw_arr,
    'utau_SS': utau_arr,
    'du_dy_SS': kavg_arr,
    'thetatau_SS': thetatau_arr,
    'dt_dy_SS': dtdy_arr
}

df = pd.DataFrame(output_data)
output_filename = 'Utau_Thetatau_SS_Results.xlsx'
df.to_excel(output_filename, index=False)

print(f"SS面计算结果已保存到：{output_filename}")
print("包含以下列：")
print("  - x_index: 流向位置索引")
print("  - tauw_SS: SS面壁面剪切应力")
print("  - utau_SS: SS面摩擦速度 (重要)")
print("  - du_dy_SS: SS面速度梯度")
print("  - thetatau_SS: SS面摩擦温度 (重要)")
print("  - dt_dy_SS: SS面温度梯度")

print(f"\n=== 计算完成 ===")
print(f"总数据点数：{len(utau_arr)}")
print(f"壁面剪切应力范围：{np.min(tauw_arr):.6f} ~ {np.max(tauw_arr):.6f}")
print(f"速度梯度范围：{np.min(kavg_arr):.6f} ~ {np.max(kavg_arr):.6f}")
print(f"温度梯度范围：{np.min(dtdy_arr):.6f} ~ {np.max(dtdy_arr):.6f}")

print("\n✅ 摩擦速度和摩擦温度计算完成！") 